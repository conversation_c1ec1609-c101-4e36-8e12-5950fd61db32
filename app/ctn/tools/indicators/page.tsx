"use client";

import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { CachedTierProtection } from "@/components/CachedTierProtection";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";

import {
  Play,
  Pause,
  RotateCcw,
  Maximize2,
  Download,
  Share2,
  Crown,
  Zap,
  Target,
  Activity,
  TrendingUp,
  Calculator,
  Search,
} from "lucide-react";
import dynamic from "next/dynamic";

import { ZStrategy } from "@/lib/ZStrategy";
import {
  useMarketDataStream,
  MarketDataPoint,
} from "@/components/EnhancedMarketDataStream";
import { calculateStrategyAccuracy, convertMarkersToStrategyMarkers } from "@/utils/strategy-accuracy";
import Link from "next/link";

const LightweightCandlestickChart = dynamic(
  () => import("@/components/LightweightCandlestickChart"),
  { ssr: false }
);

interface CandleData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface IndicatorData {
  id: string;
  name: string;
  enabled: boolean;
  color: string;
  data: { x: number; y: number }[];
}

interface Strategy {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  type: "long" | "short" | "both";
  signals: {
    buy: number;
    sell: number;
    accuracy: number;
  };
}

const availableStrategies: Strategy[] = [
  {
    id: "z_strategy",
    name: "ZStrategy",
    description:
      "Quantitative strategy with SuperTrend, VWEMA, Hull MA, and advanced filters",
    enabled: false,
    type: "both",
    signals: { buy: 0, sell: 0, accuracy: 0 },
  },
];

const supportedPairs = [
  "BTCUSDT",
  "ETHUSDT",
  "SOLUSDT",
  "ADAUSDT",
  "XRPUSDT",
  "SUIUSDT",
  "SEIUSDT",
  "RAYUSDT",
  "JUPUSDT",
  "BNBUSDT",
  "PYTHUSDT",
  "NATIXUSDT",
  "RENDERUSDT",
  "ZEUSUSDT",
  "APEUSDT",
  "BONKUSDT",
  "DOGEUSDT",
  "FLOKIUSDT",
  "PENGUUSDT",
  "PEPEUSDT",
  "SHIBUSDT",
  "WIFUSDT",
];

const timeframes = [
  { value: "1m", label: "1M" },
  { value: "5m", label: "5M" },
  { value: "15m", label: "15M" },
  { value: "1h", label: "1H" },
  { value: "4h", label: "4H" },
  { value: "1d", label: "1D" },
];

function PremiumStrategiesContent() {
  const [strategies, setStrategies] = useState<Strategy[]>(availableStrategies);
  const [selectedPair, setSelectedPair] = useState("BTCUSDT");
  const [timeframe, setTimeframe] = useState("1h");
  const [isStreaming, setIsStreaming] = useState(false);
  const [candleData, setCandleData] = useState<CandleData[]>([]);
  const [indicators, setIndicators] = useState<IndicatorData[]>([]);
  const [zStrategyMarkers, setZStrategyMarkers] = useState<any[]>([]);
  // ZStrategy state
  const [zStats, setZStats] = useState<{
    buy: number;
    sell: number;
    accuracy: number;
  }>({ buy: 0, sell: 0, accuracy: 0 });
  const [accuracyDetails, setAccuracyDetails] = useState<any[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<
    "disconnected" | "connecting" | "connected"
  >("disconnected");
  const chartRef = useRef<any>(null);

  // Add state for SMA/EMA toggles
  const [showSMA, setShowSMA] = useState(true);
  const [showEMA, setShowEMA] = useState(true);
  const [visibleRange, setVisibleRange] = useState<any>(null);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [hasMoreHistory, setHasMoreHistory] = useState(true);
  const [oldestTimestamp, setOldestTimestamp] = useState<number | null>(null);

  // Enhanced market data stream
  const marketDataStream = useMarketDataStream({
    symbol: selectedPair,
    interval: timeframe,
    onUpdate: (data: MarketDataPoint) => {
      console.log("📊 Received market data update:", data);
      const newCandle: CandleData = {
        timestamp: data.timestamp,
        open: data.open,
        high: data.high,
        low: data.low,
        close: data.close,
        volume: data.volume,
      };

      setCandleData((prev) => {
        const updated = [...prev];
        const lastIndex = updated.length - 1;

        // If it's the same timestamp, update the existing candle
        if (
          lastIndex >= 0 &&
          updated[lastIndex].timestamp === newCandle.timestamp
        ) {
          updated[lastIndex] = newCandle;
        } else {
          // Add new candle and keep only last 100
          updated.push(newCandle);
          if (updated.length > 100) {
            updated.shift();
          }
        }

        return updated;
      });
    },
    onConnect: () => {
      console.log("✅ Market data stream connected");
      setConnectionStatus("connected");
    },
    onDisconnect: () => {
      console.log("🔌 Market data stream disconnected");
      setConnectionStatus("disconnected");
    },
    onError: () => {
      console.log(
        "📊 WebSocket unavailable (normal in development), continuing with simulation"
      );
      // Don't change connection status - simulation is already running
    },
  });

  // Real data streaming function
  const startDataStream = useCallback(async () => {
    if (isStreaming) return;

    setIsStreaming(true);
    setConnectionStatus("connecting");

    try {
      // Fetch initial historical data from Binance API - Get more data for better chart coverage
      console.log(
        `🔄 Fetching initial historical data for ${selectedPair} ${timeframe}...`
      );
      const response = await fetch(
        `https://api.binance.com/api/v3/klines?symbol=${selectedPair}&interval=${timeframe}&limit=1000`
      );
      const data = await response.json();

      if (Array.isArray(data)) {
        const initialData: CandleData[] = data.map((kline: any) => ({
          timestamp: parseInt(kline[0]),
          open: parseFloat(kline[1]),
          high: parseFloat(kline[2]),
          low: parseFloat(kline[3]),
          close: parseFloat(kline[4]),
          volume: parseFloat(kline[5]),
        }));

        setCandleData(initialData);

        // Set up for infinite scrolling
        if (initialData.length > 0) {
          const oldest = Math.min(...initialData.map((d) => d.timestamp));
          setOldestTimestamp(oldest);
          setHasMoreHistory(true);
          console.log(
            `📊 Initial data loaded: ${initialData.length} candles, oldest: ${new Date(oldest).toISOString()}`
          );

          // Automatically load extensive historical data to fill gaps
          setTimeout(() => {
            console.log("🔄 Auto-loading extensive historical data...");
            loadExtensiveHistory();
          }, 1000);
        }

        // Try to connect to real-time stream first
        console.log("🔄 Attempting to connect to real-time market data stream");
        marketDataStream.connect();

        // Fallback to simulation if stream doesn't connect within 10 seconds
        setTimeout(() => {
          if (!marketDataStream.isConnected) {
            console.log(
              "⚠️ Real-time stream failed, falling back to simulation"
            );
            startSimulation();
          }
        }, 10000);
      }
    } catch (error) {
      console.error("Error fetching historical data:", error);
      console.log("🔄 API failed, starting simulation with mock data");

      // Generate mock data if API fails
      const mockData: CandleData[] = [];
      const now = Date.now();
      const timeframeMs =
        timeframe === "1m"
          ? 60000
          : timeframe === "5m"
            ? 300000
            : timeframe === "15m"
              ? 900000
              : timeframe === "1h"
                ? 3600000
                : timeframe === "4h"
                  ? 14400000
                  : 86400000;

      for (let i = 99; i >= 0; i--) {
        // Ensure unique, properly spaced timestamps
        const timestamp = now - i * timeframeMs;
        const basePrice = 45000 + Math.sin(i * 0.1) * 2000;
        const volatility = basePrice * 0.01; // 1% volatility relative to price

        const open = Math.max(
          0.01,
          basePrice + (Math.random() - 0.5) * volatility
        );
        const close = Math.max(0.01, open + (Math.random() - 0.5) * volatility);
        const high = Math.max(open, close) + Math.random() * volatility * 0.3;
        const low = Math.min(open, close) - Math.random() * volatility * 0.3;
        const volume = Math.random() * 1000000;

        // Ensure proper OHLC relationships and unique timestamp
        const validatedCandle = {
          timestamp: timestamp, // This ensures proper spacing
          open: Number(open.toFixed(2)),
          high: Number(Math.max(open, close, high).toFixed(2)),
          low: Number(Math.max(0.01, Math.min(open, close, low)).toFixed(2)),
          close: Number(close.toFixed(2)),
          volume: Number(volume.toFixed(0)),
        };

        mockData.push(validatedCandle);
      }

      setCandleData(mockData);
      startSimulation();
    }
  }, [selectedPair, timeframe, isStreaming, marketDataStream]);

  const stopDataStream = useCallback(() => {
    setIsStreaming(false);
    setConnectionStatus("disconnected");
    marketDataStream.disconnect();
  }, [marketDataStream]);

  // Handle real-time updates from WebSocket
  const handleStreamUpdate = useCallback((newBar: any) => {
    console.log("📊 Received stream update:", newBar);
    const candleUpdate: CandleData = {
      timestamp: newBar.time * 1000, // Convert back to milliseconds
      open: newBar.open,
      high: newBar.high,
      low: newBar.low,
      close: newBar.close,
      volume: 0, // Volume not provided in this format
    };

    setCandleData((prev) => {
      return [...prev.slice(0, -1), candleUpdate]; // Replace last candle
    });
  }, []);

  // Fallback: Simulate live updates if WebSocket fails
  const startSimulation = useCallback(() => {
    console.log("🔄 Starting price simulation fallback");
    setConnectionStatus("connected");

    // Calculate timeframe in milliseconds
    const getTimeframeMs = () => {
      switch (timeframe) {
        case "1m":
          return 60000;
        case "5m":
          return 300000;
        case "15m":
          return 900000;
        case "1h":
          return 3600000;
        case "4h":
          return 14400000;
        case "1d":
          return 86400000;
        default:
          return 3600000;
      }
    };

    const timeframeMs = getTimeframeMs();
    const updateInterval = Math.min(2000, timeframeMs / 10); // Update more frequently for shorter timeframes

    const interval = setInterval(() => {
      if (!isStreaming) {
        clearInterval(interval);
        return;
      }

      setCandleData((prev) => {
        if (prev.length === 0) return prev;

        const latest = prev[prev.length - 1];

        // Create more realistic price movements
        const volatility = latest.close * 0.001; // 0.1% volatility
        const change = (Math.random() - 0.5) * volatility * 2;
        const newPrice = Math.max(0.01, latest.close + change); // Ensure positive price

        // Ensure proper OHLC relationships
        const updatedCandle = {
          timestamp: latest.timestamp, // Keep original timestamp for the current candle
          open: latest.open, // Open doesn't change during candle
          high: Math.max(latest.high, newPrice),
          low: Math.min(latest.low, newPrice),
          close: newPrice,
          volume: latest.volume + Math.random() * 1000, // Simulate volume increase
        };

        // Validate OHLC relationships
        if (updatedCandle.high < updatedCandle.low) {
          updatedCandle.high = updatedCandle.low;
        }
        if (updatedCandle.open > updatedCandle.high) {
          updatedCandle.open = updatedCandle.high;
        }
        if (updatedCandle.open < updatedCandle.low) {
          updatedCandle.open = updatedCandle.low;
        }
        if (updatedCandle.close > updatedCandle.high) {
          updatedCandle.close = updatedCandle.high;
        }
        if (updatedCandle.close < updatedCandle.low) {
          updatedCandle.close = updatedCandle.low;
        }

        return [...prev.slice(0, -1), updatedCandle];
      });
    }, updateInterval);

    return () => clearInterval(interval);
  }, [isStreaming, timeframe]);

  // All available indicators - always calculated but enabled/disabled by toggle
  const allAvailableIndicators = useMemo(() => {
    if (!candleData || candleData.length === 0) return [];

    const indicatorData: IndicatorData[] = [];

    // SMA(20) - always calculate
    const smaData = candleData.map((candle, index) => {
      if (index < 19) return { x: candle.timestamp, y: candle.close };
      const sum = candleData
        .slice(index - 19, index + 1)
        .reduce((acc, c) => acc + c.close, 0);
      return { x: candle.timestamp, y: sum / 20 };
    });
    indicatorData.push({
      id: "sma_20",
      name: "SMA(20)",
      enabled: showSMA, // Use state for enabled status
      color: "#3b82f6",
      data: smaData,
    });

    // EMA(50) - always calculate
    let ema = candleData[0]?.close || 45000;
    const multiplier = 2 / (50 + 1);
    const emaData = candleData.map((candle, index) => {
      if (index === 0) {
        ema = candle.close;
      } else {
        ema = candle.close * multiplier + ema * (1 - multiplier);
      }
      return { x: candle.timestamp, y: ema };
    });
    indicatorData.push({
      id: "ema_50",
      name: "EMA(50)",
      enabled: showEMA, // Use state for enabled status
      color: "#ef4444",
      data: emaData,
    });

    return indicatorData;
  }, [candleData, showSMA, showEMA]);

  // Filtered indicators for chart rendering (only enabled ones)
  const memoizedIndicators = useMemo(() => {
    return allAvailableIndicators.filter((indicator) => indicator.enabled);
  }, [allAvailableIndicators]);

  // Memoized ZStrategy calculation - now creates markers instead of primitives
  const memoizedZStrategy = useMemo(() => {
    const zEnabled = strategies.find((s) => s.id === "z_strategy" && s.enabled);
    console.log("ZStrategy memoization triggered:", {
      zEnabled: !!zEnabled,
      candleDataLength: candleData?.length,
    });

    if (!zEnabled || !candleData || candleData.length === 0) {
      console.log("ZStrategy disabled or no data, returning empty markers");
      return { markers: [], stats: { buy: 0, sell: 0, accuracy: 0 } };
    }

    console.log("Running ZStrategy with", candleData.length, "candles");
    const z = new ZStrategy();
    const zResult = z.executeStrategy(candleData);

    console.log("ZStrategy execution result:", {
      signalsLength: zResult.signals?.length || 0,
      firstSignal: zResult.signals?.[0],
      tradesLength: zResult.trades?.length || 0,
    });

    if (zResult.signals && zResult.signals.length > 0) {
      // Transform ZStrategy signals to Lightweight Charts markers format
      const markers: any[] = [];

      // Filter signals to only those in the visible time range and limit quantity
      const visibleSignals = zResult.signals
        .filter((signal) => signal.filtersPass && (signal.buy || signal.sell))
        .slice(-50); // Only show last 50 signals to prevent overcrowding

      console.log(
        `Filtered ${visibleSignals.length} signals from ${zResult.signals.length} total signals`
      );

      visibleSignals.forEach((signal) => {
        // Convert timestamp to time format expected by Lightweight Charts
        const time = Math.floor(signal.timestamp / 1000);
        const price = signal.price || signal.stLine;

        if (signal.buy) {
          markers.push({
            time: time,
            position: "belowBar",
            color: "#10b981", // Green for buy
            shape: "arrowUp",
            text: `Buy @ ${price.toFixed(2)}`,
            id: `buy_${signal.timestamp}`,
          });
        }

        if (signal.sell) {
          markers.push({
            time: time,
            position: "aboveBar",
            color: "#ef4444", // Red for sell
            shape: "arrowDown",
            text: `Sell @ ${price.toFixed(2)}`,
            id: `sell_${signal.timestamp}`,
          });
        }
      });

      console.log("Created ZStrategy markers:", markers.length, "markers");

      // Calculate stats
      const buy = zResult.signals.filter((s) => s.buy && s.filtersPass).length;
      const sell = zResult.signals.filter(
        (s) => s.sell && s.filtersPass
      ).length;
      
      // Use new accuracy calculation based on marker placement
      const strategyMarkers = convertMarkersToStrategyMarkers(markers);
      const priceData = candleData.map(c => ({ time: Math.floor(c.timestamp / 1000), close: c.close }));
      const accuracyResult = calculateStrategyAccuracy(strategyMarkers, priceData);
      
      console.log("📊 ZStrategy accuracy calculation:", {
        totalSignals: accuracyResult.totalSignals,
        accurateSignals: accuracyResult.accurateSignals,
        accuracy: accuracyResult.accuracy
      });

      return {
        markers: markers,
        stats: { buy, sell, accuracy: accuracyResult.accuracy },
        accuracyDetails: accuracyResult.details
      };
    }

    console.log("No ZStrategy signals generated or empty result");
    return { markers: [], stats: { buy: 0, sell: 0, accuracy: 0 } };
  }, [candleData, strategies]);

  // Update indicators when memoized values change
  useEffect(() => {
    setIndicators(memoizedIndicators);
  }, [memoizedIndicators]);

  // Update ZStrategy when memoized values change
  useEffect(() => {
    setZStrategyMarkers(memoizedZStrategy.markers);
    setZStats(memoizedZStrategy.stats);
    if (memoizedZStrategy.accuracyDetails) {
      setAccuracyDetails(memoizedZStrategy.accuracyDetails);
    }
  }, [memoizedZStrategy]);

  // Function to load more historical data with improved error handling
  const loadMoreHistory = useCallback(async () => {
    if (isLoadingHistory || !hasMoreHistory || !oldestTimestamp) {
      console.log("Skipping history load:", {
        isLoadingHistory,
        hasMoreHistory,
        oldestTimestamp,
      });
      return;
    }

    setIsLoadingHistory(true);
    console.log("Loading more history before timestamp:", new Date(oldestTimestamp).toISOString());

    try {
      // Request more data per batch for better performance
      const response = await fetch(
        `https://api.binance.com/api/v3/klines?symbol=${selectedPair}&interval=${timeframe}&limit=1000&endTime=${oldestTimestamp - 1}`
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (Array.isArray(data) && data.length > 0) {
        const historicalData: CandleData[] = data.map((kline: any) => ({
          timestamp: parseInt(kline[0]),
          open: parseFloat(kline[1]),
          high: parseFloat(kline[2]),
          low: parseFloat(kline[3]),
          close: parseFloat(kline[4]),
          volume: parseFloat(kline[5]),
        }));

        console.log(`📊 Loaded ${historicalData.length} historical candles, oldest: ${new Date(Math.min(...historicalData.map(d => d.timestamp))).toISOString()}`);

        setCandleData((prev) => {
          const combined = [...historicalData, ...prev];
          // Remove duplicates based on timestamp
          const unique = combined
            .filter(
              (candle, index, arr) =>
                arr.findIndex((c) => c.timestamp === candle.timestamp) === index
            )
            .sort((a, b) => a.timestamp - b.timestamp);

          console.log(
            `📊 Combined data: ${combined.length} -> ${unique.length} unique candles`
          );
          return unique;
        });

        // Update oldest timestamp for next load
        const newOldest = Math.min(...historicalData.map((d) => d.timestamp));
        setOldestTimestamp(newOldest);

        // More lenient check - only stop if we get significantly less data
        if (data.length < 100) { // Changed from 500 to 100 for more aggressive loading
          setHasMoreHistory(false);
          console.log("📊 Reached the beginning of available data (got less than 100 candles)");
        }
      } else {
        setHasMoreHistory(false);
        console.log("📊 No more historical data available from API");
      }
    } catch (error) {
      console.error("❌ Error loading historical data:", error);
      // Don't immediately disable loading on error - retry might work
      setTimeout(() => {
        if (hasMoreHistory) {
          console.log("🔄 Retrying historical data load after error...");
        }
      }, 5000);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [
    selectedPair,
    timeframe,
    isLoadingHistory,
    hasMoreHistory,
    oldestTimestamp,
  ]);

  // Aggressive auto-loading function to ensure we have extensive historical data
  const loadExtensiveHistory = useCallback(async () => {
    if (!oldestTimestamp || isLoadingHistory) return;

    console.log("🔄 Loading extensive historical data to fill gaps...");
    setIsLoadingHistory(true);

    try {
      // Calculate how much data we want (e.g., 2+ years for better coverage)
      const timeframeMs =
        timeframe === "1m"
          ? 60000
          : timeframe === "5m"
            ? 300000
            : timeframe === "15m"
              ? 900000
              : timeframe === "1h"
                ? 3600000
                : timeframe === "4h"
                  ? 14400000
                  : timeframe === "1d"
                    ? 86400000
                    : 3600000;

      // Load much more historical data - 2 years instead of 6 months
      const twoYearsAgo = Date.now() - 2 * 365 * 24 * 60 * 60 * 1000; // 2 years ago
      const targetTimestamp = Math.max(
        twoYearsAgo,
        oldestTimestamp - 500 * timeframeMs // Load more data points
      );

      let currentOldest = oldestTimestamp;
      let totalLoaded = 0;
      const maxBatches = 50; // Increased from 10 to 50 for more historical data

      for (
        let batch = 0;
        batch < maxBatches && currentOldest > targetTimestamp;
        batch++
      ) {
        try {
          console.log(
            `📊 Loading batch ${batch + 1}/${maxBatches}, current oldest: ${new Date(currentOldest).toISOString()}`
          );

          const response = await fetch(
            `https://api.binance.com/api/v3/klines?symbol=${selectedPair}&interval=${timeframe}&limit=1000&endTime=${currentOldest - 1}`
          );
          const data = await response.json();

          if (!Array.isArray(data) || data.length === 0) {
            console.log("📊 No more historical data available");
            break;
          }

          const historicalData: CandleData[] = data.map((kline: any) => ({
            timestamp: parseInt(kline[0]),
            open: parseFloat(kline[1]),
            high: parseFloat(kline[2]),
            low: parseFloat(kline[3]),
            close: parseFloat(kline[4]),
            volume: parseFloat(kline[5]),
          }));

          setCandleData((prev) => {
            const combined = [...historicalData, ...prev];
            const unique = combined
              .filter(
                (candle, index, arr) =>
                  arr.findIndex((c) => c.timestamp === candle.timestamp) ===
                  index
              )
              .sort((a, b) => a.timestamp - b.timestamp);

            return unique;
          });

          currentOldest = Math.min(...historicalData.map((d) => d.timestamp));
          setOldestTimestamp(currentOldest);
          totalLoaded += historicalData.length;

          console.log(
            `📊 Batch ${batch + 1} loaded: ${historicalData.length} candles, total: ${totalLoaded}`
          );

          // Small delay between batches to avoid rate limiting
          if (batch < maxBatches - 1) {
            await new Promise((resolve) => setTimeout(resolve, 200));
          }
        } catch (batchError) {
          console.error(`❌ Error loading batch ${batch + 1}:`, batchError);
          // Don't break immediately - try a few more times in case it's a temporary error
          if (batch > 3) { // Only break after trying at least 4 batches
            console.log("🛑 Too many batch errors, stopping extensive loading");
            break;
          }
          // Add delay before next attempt
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      console.log(
        `✅ Extensive history loading complete: ${totalLoaded} total candles loaded, oldest: ${new Date(currentOldest).toISOString()}`
      );

      // Update hasMoreHistory based on whether we reached our target or got no data
      if (currentOldest <= targetTimestamp || totalLoaded === 0) {
        setHasMoreHistory(false);
        console.log("📊 Reached target timestamp or no more data available");
      }
    } catch (error) {
      console.error("❌ Error loading extensive history:", error);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [selectedPair, timeframe, oldestTimestamp, isLoadingHistory]);

  const toggleStrategy = (id: string) => {
    console.log("Toggling strategy:", id);
    setStrategies((prev) => {
      const updated = prev.map((strategy) =>
        strategy.id === id
          ? { ...strategy, enabled: !strategy.enabled }
          : strategy
      );
      console.log(
        "Updated strategies:",
        updated.map((s) => ({ id: s.id, enabled: s.enabled }))
      );
      return updated;
    });
  };

  // Handle pair/timeframe changes for active streams only
  useEffect(() => {
    // Only update the stream if it's already connected and streaming
    if (marketDataStream.isConnected && isStreaming) {
      console.log(`Updating stream for ${selectedPair} ${timeframe}...`);
      marketDataStream.updateSymbol(selectedPair, timeframe);

      // Restart data fetching with new parameters
      startDataStream();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPair, timeframe]); // Only depend on pair and timeframe

  // Patch ZStrategy stats into the panel if enabled
  const patchedStrategies = strategies.map((s) =>
    s.id === "z_strategy" && s.enabled
      ? {
          ...s,
          signals: {
            buy: zStats.buy,
            sell: zStats.sell,
            accuracy: zStats.accuracy,
          },
        }
      : s
  );

  return (
    <div className="min-h-screen bg-background">
      <div className="w-full px-4 py-6 max-w-none">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Zap className="h-8 w-8 text-purple-500" />
              Premium Trading Strategies
            </h1>
            <p className="text-muted-foreground mt-2">
              Professional algorithmic strategies with live market data
              streaming
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-purple-600">
              <Crown className="h-4 w-4 mr-1" />
              Tier 2+ Exclusive
            </Badge>
            <Badge
              variant="outline"
              className={`${
                connectionStatus === "connected"
                  ? "text-green-600 bg-green-50"
                  : connectionStatus === "connecting"
                    ? "text-yellow-600 bg-yellow-50"
                    : "text-red-600 bg-red-50"
              }`}
            >
              <Activity className="h-3 w-3 mr-1" />
              {connectionStatus === "connected"
                ? marketDataStream.isConnected
                  ? "Live WebSocket"
                  : "Live Simulation"
                : connectionStatus === "connecting"
                  ? "Connecting"
                  : "Disconnected"}
            </Badge>
          </div>
        </div>

        {/* Main Layout */}
        <div className="space-y-6">
          {/* Chart Area - Full Width */}
          <div className="space-y-4">
            {/* Chart Controls */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Select
                      value={selectedPair}
                      onValueChange={setSelectedPair}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {supportedPairs.map((pair) => (
                          <SelectItem key={pair} value={pair}>
                            {pair}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select value={timeframe} onValueChange={setTimeframe}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {timeframes.map((tf) => (
                          <SelectItem key={tf.value} value={tf.value}>
                            {tf.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Badge variant="outline" className="text-xs">
                      <Target className="h-3 w-3 mr-1" />
                      {strategies.filter((s) => s.enabled).length} Active
                    </Badge>

                    {isLoadingHistory && (
                      <Badge
                        variant="outline"
                        className="text-xs text-blue-600 bg-blue-50"
                      >
                        <Activity className="h-3 w-3 mr-1 animate-spin" />
                        Loading
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    {/* Strategy Dropdown */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="liquid-glass"
                          size="sm"
                          title="Trading Strategies"
                        >
                          <Target className="h-4 w-4 mr-1" />
                          Strategies
                          <Badge variant="secondary" className="ml-1 text-xs">
                            {strategies.filter((s) => s.enabled).length}
                          </Badge>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="center" className="w-64">
                        <DropdownMenuLabel className="flex items-center gap-2">
                          <Target className="h-4 w-4" />
                          Trading Strategies
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {strategies.map((strategy) => (
                          <DropdownMenuCheckboxItem
                            key={strategy.id}
                            checked={strategy.enabled}
                            onCheckedChange={() => toggleStrategy(strategy.id)}
                            className="flex items-center justify-between"
                          >
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {strategy.name}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {strategy.description.slice(0, 40)}...
                              </span>
                            </div>
                            <Badge
                              variant="outline"
                              className={`text-xs ml-2 ${
                                strategy.type === "long"
                                  ? "text-green-600"
                                  : strategy.type === "short"
                                    ? "text-red-600"
                                    : "text-blue-600"
                              }`}
                            >
                              {strategy.type.toUpperCase()}
                            </Badge>
                          </DropdownMenuCheckboxItem>
                        ))}
                        {strategies.filter((s) => s.enabled).length === 0 && (
                          <DropdownMenuItem
                            disabled
                            className="text-center text-muted-foreground"
                          >
                            No strategies enabled
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>

                    {/* Indicators Dropdown */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="liquid-glass"
                          size="sm"
                          title="Technical Indicators"
                        >
                          <Activity className="h-4 w-4 mr-1" />
                          Indicators
                          <Badge variant="secondary" className="ml-1 text-xs">
                            {
                              allAvailableIndicators.filter((i) => i.enabled)
                                .length
                            }
                          </Badge>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="center" className="w-56">
                        <DropdownMenuLabel className="flex items-center gap-2">
                          <Activity className="h-4 w-4" />
                          Technical Indicators
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {allAvailableIndicators.map((indicator) => (
                          <DropdownMenuCheckboxItem
                            key={indicator.id}
                            checked={indicator.enabled}
                            onCheckedChange={
                              indicator.id === "sma_20"
                                ? setShowSMA
                                : setShowEMA
                            }
                            className="flex items-center justify-between"
                          >
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: indicator.color }}
                              />
                              <span>{indicator.name}</span>
                            </div>
                          </DropdownMenuCheckboxItem>
                        ))}
                        {allAvailableIndicators.length === 0 && (
                          <DropdownMenuItem
                            disabled
                            className="text-center text-muted-foreground"
                          >
                            No indicators available
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          disabled
                          className="text-xs text-muted-foreground"
                        >
                          {zStrategyMarkers.length > 0 && (
                            <span>
                              {zStrategyMarkers.length} ZStrategy signals active
                            </span>
                          )}
                          {zStrategyMarkers.length === 0 && (
                            <span>No ZStrategy signals</span>
                          )}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button
                      variant="liquid-glass"
                      size="sm"
                      onClick={isStreaming ? stopDataStream : startDataStream}
                      title={
                        isStreaming
                          ? "Pause live updates"
                          : "Resume live updates"
                      }
                    >
                      {isStreaming ? (
                        <Pause className="h-4 w-4" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="liquid-glass"
                      size="sm"
                      onClick={loadExtensiveHistory}
                      disabled={isLoadingHistory}
                      title="Load more historical data"
                    >
                      <RotateCcw
                        className={`h-4 w-4 ${isLoadingHistory ? "animate-spin" : ""}`}
                      />
                    </Button>

                    <Button variant="liquid-glass" size="sm">
                      <Maximize2 className="h-4 w-4" />
                    </Button>
                    <Button variant="liquid-glass" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="liquid-glass" size="sm">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Live Chart with LightweightCandlestickChart - Full Width & Optimized Height */}
            <Card className="h-[calc(100vh-180px)] min-h-[700px]">
              <CardContent className="h-full p-6">
                <div className="h-full bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-950 rounded-lg p-4 relative">
                  <div
                    style={{
                      width: "100%",
                      minWidth: 320,
                      maxWidth: "100%",
                      margin: "0 auto",
                    }}
                  >
                    <LightweightCandlestickChart
                      ref={chartRef}
                      data={(() => {
                        console.log(
                          "📊 Starting chart data preparation with",
                          candleData.length,
                          "raw candles"
                        );
                        console.log(
                          "Raw data sample (first 3 candles):",
                          candleData.slice(0, 3)
                        );
                        console.log(
                          "Raw data sample (last 3 candles):",
                          candleData.slice(-3)
                        );

                        // Step 1: Take last 500 candles instead of 100
                        const slicedData = candleData.slice(-500);
                        console.log(
                          "Step 1 - After slice(-500):",
                          slicedData.length,
                          "candles"
                        );

                        // Step 2: Transform format
                        const transformedData = slicedData.map((c) => ({
                          time: Math.floor(c.timestamp / 1000),
                          open: Number(c.open.toFixed(2)),
                          high: Number(c.high.toFixed(2)),
                          low: Number(c.low.toFixed(2)),
                          close: Number(c.close.toFixed(2)),
                          originalTimestamp: c.timestamp,
                        }));
                        console.log(
                          "Step 2 - After transformation:",
                          transformedData.length,
                          "candles"
                        );
                        console.log(
                          "Sample transformed candle:",
                          transformedData[0]
                        );

                        // Step 3: Filter valid data (this might be the issue!)
                        const filteredData = transformedData.filter((c) => {
                          const isValid =
                            c.time > 0 &&
                            c.open > 0 &&
                            c.high > 0 &&
                            c.low > 0 &&
                            c.close > 0 &&
                            !isNaN(c.time) &&
                            !isNaN(c.open) &&
                            !isNaN(c.high) &&
                            !isNaN(c.low) &&
                            !isNaN(c.close);

                          if (!isValid) {
                            console.warn("❌ Invalid candle filtered out:", c);
                          }
                          return isValid;
                        });
                        console.log(
                          "Step 3 - After filtering:",
                          filteredData.length,
                          "candles"
                        );

                        // Step 4: Sort by time
                        const sortedData = filteredData.sort(
                          (a, b) => a.time - b.time
                        );
                        console.log(
                          "Step 4 - After sorting:",
                          sortedData.length,
                          "candles"
                        );

                        // Step 5: Remove duplicates (this might be overly aggressive!)
                        const uniqueData = sortedData.reduce(
                          (acc: any[], current) => {
                            if (acc.length === 0) {
                              acc.push(current);
                            } else {
                              const lastCandle = acc[acc.length - 1];
                              if (current.time > lastCandle.time) {
                                acc.push(current);
                              } else if (current.time === lastCandle.time) {
                                console.warn(
                                  "⚠️ Duplicate timestamp found:",
                                  current.time,
                                  "- replacing with newer data"
                                );
                                acc[acc.length - 1] = current; // Replace with current
                              } else {
                                console.warn(
                                  "⚠️ Out of order timestamp:",
                                  current.time,
                                  "vs",
                                  lastCandle.time
                                );
                              }
                            }
                            return acc;
                          },
                          []
                        );

                        console.log(
                          "Step 5 - Final unique data:",
                          uniqueData.length,
                          "candles"
                        );
                        console.log("📈 Time range:", {
                          first: uniqueData[0]?.time,
                          last: uniqueData[uniqueData.length - 1]?.time,
                          span:
                            uniqueData[uniqueData.length - 1]?.time -
                            uniqueData[0]?.time,
                          spanHours: (
                            (uniqueData[uniqueData.length - 1]?.time -
                              uniqueData[0]?.time) /
                            3600
                          ).toFixed(1),
                        });

                        return uniqueData;
                      })()}
                      options={{}}
                      chartOptions={{
                        layout: {
                          background: { type: "solid", color: "transparent" },
                          textColor: "#64748b",
                        },
                        grid: {
                          vertLines: { color: "rgba(100,116,139,0.1)" },
                          horzLines: { color: "rgba(100,116,139,0.1)" },
                        },
                        timeScale: { timeVisible: true, secondsVisible: false },
                        rightPriceScale: { borderColor: "#64748b22" },
                        crosshair: { mode: 0 },
                      }}
                      indicators={indicators
                        .filter((ind) => ind.enabled) // Only include enabled indicators
                        .map((ind) => ({
                          id: ind.id,
                          apply: ({ addLineSeries }) => {
                            try {
                              const line = addLineSeries({
                                color: ind.color,
                                lineWidth: 2,
                                title: ind.name,
                              });
                              const lineData = ind.data
                                .map((point) => ({
                                  time: Math.floor(point.x / 1000),
                                  value: Number(point.y.toFixed(2)),
                                }))
                                .filter(
                                  (item) => item.time > 0 && !isNaN(item.value)
                                )
                                .sort((a, b) => a.time - b.time)
                                .reduce((acc: any[], current) => {
                                  if (
                                    acc.length === 0 ||
                                    current.time > acc[acc.length - 1].time
                                  ) {
                                    acc.push(current);
                                  }
                                  return acc;
                                }, []);
                              if (lineData.length > 0) {
                                line.setData(lineData);
                              }
                              return line; // Return the series for cleanup
                            } catch (err) {
                              console.warn(
                                "Failed to add indicator line:",
                                ind.id,
                                err
                              );
                              return null;
                            }
                          },
                        }))}
                      seriesMarkers={zStrategyMarkers}
                      height={Math.max(
                        600,
                        typeof window !== "undefined"
                          ? window.innerHeight - 260
                          : 600
                      )}
                      width="100%"
                      enableStreaming={isStreaming}
                      onStreamingUpdate={handleStreamUpdate}
                      onVisibleLogicalRangeChange={(logicalRange) => {
                        setVisibleRange(logicalRange);
                        console.log("Visible range changed:", logicalRange);

                        // Implement infinite history loading using time-based calculations
                        if (
                          logicalRange &&
                          typeof logicalRange.from === "number" &&
                          candleData.length > 0
                        ) {
                          // Convert logical indices to actual timestamps
                          const visibleStartIndex = Math.max(0, Math.floor(logicalRange.from));

                          const visibleStartTime = candleData[visibleStartIndex]?.timestamp;
                          const earliestLoadedTime = candleData[0]?.timestamp;
                          const latestLoadedTime = candleData[candleData.length - 1]?.timestamp;

                          if (visibleStartTime && earliestLoadedTime && latestLoadedTime) {
                            // Calculate time-based proximity to beginning
                            const totalLoadedTimeSpan = latestLoadedTime - earliestLoadedTime;
                            const timeFromEarliest = visibleStartTime - earliestLoadedTime;
                            const proximityToBeginning = totalLoadedTimeSpan > 0 ? timeFromEarliest / totalLoadedTimeSpan : 0;

                            console.log("Time-based scroll check:", {
                              visibleStartTime: new Date(visibleStartTime).toISOString(),
                              earliestLoadedTime: new Date(earliestLoadedTime).toISOString(),
                              proximityToBeginning: proximityToBeginning.toFixed(3),
                              threshold: 0.2,
                              totalTimeSpanHours: (totalLoadedTimeSpan / (1000 * 60 * 60)).toFixed(1)
                            });

                            // Trigger loading when within 20% of earliest time (more aggressive than 10% of data points)
                            if (
                              proximityToBeginning < 0.2 &&
                              !isLoadingHistory &&
                              hasMoreHistory
                            ) {
                              console.log(
                                "🔄 Loading more history due to time-based scroll position"
                              );
                              loadMoreHistory();
                            }
                          }
                        }
                      }}
                    >
                      {/* Enhanced WebSocket streaming with fallback simulation */}
                    </LightweightCandlestickChart>
                  </div>
                  {/* Chart Info, Legends, and Signals can be re-added here as overlays if needed */}
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Performance Summary */}
            <div className="max-w-6xl mx-auto">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Calculator className="h-4 w-4" />
                    Strategy Performance Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Main Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center space-y-1">
                      <div className="text-xs text-muted-foreground">Total Signals</div>
                      <div className="text-lg font-bold">
                        {strategies.reduce(
                          (sum, s) => sum + s.signals.buy + s.signals.sell,
                          0
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {zStats.buy} Buy • {zStats.sell} Sell
                      </div>
                    </div>
                    
                    <div className="text-center space-y-1">
                      <div className="text-xs text-muted-foreground">Strategy Accuracy</div>
                      <div className={`text-lg font-bold ${
                        zStats.accuracy >= 70 ? 'text-green-500' : 
                        zStats.accuracy >= 50 ? 'text-yellow-500' : 'text-red-500'
                      }`}>
                        {zStats.accuracy}%
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Based on signal placement
                      </div>
                    </div>
                    
                    <div className="text-center space-y-1">
                      <div className="text-xs text-muted-foreground">Active Strategies</div>
                      <div className="text-lg font-bold">
                        {strategies.filter((s) => s.enabled).length}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        of {strategies.length} available
                      </div>
                    </div>
                  </div>

                  {/* Accuracy Details */}
                  {accuracyDetails.length > 0 && (
                    <div className="border-t pt-3">
                      <div className="text-xs font-medium mb-2 flex items-center gap-2">
                        <Target className="h-3 w-3" />
                        Signal Accuracy Analysis
                      </div>
                      <div className="space-y-1 max-h-48 overflow-y-auto">
                        {accuracyDetails.slice(-15).map((detail, index) => (
                          <div 
                            key={index}
                            className={`text-xs p-2 rounded border ${
                              detail.isAccurate 
                                ? 'bg-green-50 border-green-200 text-green-700' 
                                : 'bg-red-50 border-red-200 text-red-700'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium">
                                {detail.isAccurate ? '✓' : '✗'} Signal #{accuracyDetails.length - 15 + index + 1}
                              </span>
                              <span className="text-xs opacity-75">
                                {new Date(detail.marker.time * 1000).toLocaleTimeString()}
                              </span>
                            </div>
                            <div className="text-xs opacity-75 mt-1">
                              {detail.reason}
                            </div>
                          </div>
                        ))}
                      </div>
                      {accuracyDetails.length > 15 && (
                        <div className="text-xs text-center text-muted-foreground mt-2">
                          Showing latest 15 of {accuracyDetails.length} signals analyzed
                        </div>
                      )}
                    </div>
                  )}

                  {/* Performance Indicators */}
                  <div className="flex items-center justify-between text-xs pt-2 border-t">
                    <div className="flex items-center gap-4">
                      <div className={`flex items-center gap-1 ${
                        zStats.accuracy >= 70 ? 'text-green-600' : 
                        zStats.accuracy >= 50 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        <div className={`w-2 h-2 rounded-full ${
                          zStats.accuracy >= 70 ? 'bg-green-500' : 
                          zStats.accuracy >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                        }`} />
                        <span>
                          {zStats.accuracy >= 70 ? 'Excellent' : 
                           zStats.accuracy >= 50 ? 'Good' : 'Needs Improvement'}
                        </span>
                      </div>
                    </div>
                    <div className="text-muted-foreground">
                      Live calculation based on {selectedPair} {timeframe}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function StrategiesPage() {
  return (
    <CachedTierProtection pagePath="/ctn/tools/indicators">
      <PremiumStrategiesContent />
    </CachedTierProtection>
  );
}
