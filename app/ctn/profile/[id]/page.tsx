import { createClient } from "@/utils/supabase/server";
import { ProfileDisplay } from "@/components/ProfileDisplay";
import { notFound, redirect } from "next/navigation";
import { Suspense } from "react";
import { UserProfile as Profile, Article } from "@/types";
import { Metadata } from "next";
import { ErrorBoundary } from "@/components/ErrorBoundary";

interface DatabaseArticle extends Omit<Article, "id"> {
  id: number;
}

interface ImagePosition {
  x: number;
  y: number;
  scale: number;
}

type Props = {
  params: Promise<{ id: string }>;
};

const DEFAULT_IMAGE_POSITION: ImagePosition = {
  x: 0,
  y: 0,
  scale: 1,
};

export const revalidate = 3600; // Revalidate every hour

export async function generateMetadata(props: Props): Promise<Metadata> {
  const { params } = props;
  const { id } = await params;
  const supabase = await createClient();
  const profileId = id;

  try {
    const { data: profile, error } = await (supabase as any)
      .from("users")
      .select("username")
      .eq("user_id", profileId)
      .single();

    if (error) {
      console.error("Metadata fetch error:", error);
      return {
        title: "Profile - CTN",
        description: "View user profile on CTN",
      };
    }

    return {
      title: profile ? `${profile.username}'s Profile - CTN` : "Profile - CTN",
      description: profile
        ? `View ${profile.username}'s profile and articles on CTN`
        : "View user profile on CTN",
      openGraph: {
        title: profile
          ? `${profile.username}'s Profile - CTN`
          : "Profile - CTN",
        description: profile
          ? `View ${profile.username}'s profile and articles on CTN`
          : "View user profile on CTN",
        type: "profile",
        url: `/ctn/profile/${profileId}`,
      },
    };
  } catch (error) {
    console.error("Metadata generation error:", error);
    return {
      title: "Profile - CTN",
      description: "View user profile on CTN",
    };
  }
}

export default async function ProfilePage(props: Props) {
  const supabase = await createClient();
  const { params } = props;
  const { id } = await params;
  const profileId = id;

  try {
    // Get current user
    const {
      data: { user: currentUser },
      error: authError,
    } = await (supabase as any).auth.getUser();

    if (authError) {
      console.error("Auth error:", authError);
      throw new Error(`auth-error: ${authError.message}`);
    }

    // Fetch profile data
    const { data: profileData, error: profileError } = await (supabase as any)
      .from("users")
      .select("*")
      .eq("user_id", profileId)
      .single();

    if (profileError || !profileData) {
      console.error("Profile error:", profileError);
      notFound();
      return;
    }

    // Parallel fetch for related data
    const [
      followersCountResult,
      followingCountResult,
      articlesResult,
      isFollowingResult,
    ] = await Promise.all([
      (supabase as any)
        .from("follows")
        .select("*", { count: "exact", head: true })
        .eq("following_id", profileId),
      (supabase as any)
        .from("follows")
        .select("*", { count: "exact", head: true })
        .eq("follower_id", profileId),
      (supabase as any)
        .from("articles")
        .select(
          `
            id,
            title,
            content,
            timestamp,
            created_at,
            updated_at,
            likes,
            shares,
            comments_count,
            author,
            author_avatar,
            author_id,
            image_url,
            tags,
            views
          `
        )
        .eq("author_id", profileId)
        .order("created_at", { ascending: false }),
      currentUser
        ? (supabase as any)
            .from("follows")
            .select("*", { count: "exact", head: true })
            .eq("follower_id", currentUser.id)
            .eq("following_id", profileId)
        : Promise.resolve({ count: 0, data: [] }),
    ]);

    // Check for errors in parallel fetches
    const parallelErrors = [
      followersCountResult.error && {
        type: "followers",
        error: followersCountResult.error,
      },
      followingCountResult.error && {
        type: "following",
        error: followingCountResult.error,
      },
      articlesResult.error && { type: "articles", error: articlesResult.error },
      "error" in isFollowingResult &&
        isFollowingResult.error && {
          type: "isFollowing",
          error: isFollowingResult.error,
        },
    ].filter(Boolean);

    if (parallelErrors.length > 0) {
      console.error("Parallel fetch errors:", parallelErrors);
      throw new Error(`data-fetch-error: Multiple data fetch errors occurred`);
    }

    // Transform profile data with all available fields
    const enrichedProfile: Profile = {
      id: profileData.id,
      user_id: profileData.user_id,
      banner_url: profileData.banner_url,
      avatar_url: profileData.avatar_url,
      username: profileData.username,
      bio: profileData.bio,
      followers_count: followersCountResult.count || 0,
      following_count: followingCountResult.count || 0,
      article_count: profileData.article_count || 0,
      is_followed: (isFollowingResult.data?.length ?? 0) > 0,
      created_at: profileData.created_at,
      updated_at: profileData.updated_at,
      email: profileData.email,
      full_name: profileData.full_name,
      points: profileData.points || 0,
      avatar_position:
        (profileData.avatar_position as ImagePosition) ??
        DEFAULT_IMAGE_POSITION,
      banner_position:
        (profileData.banner_position as ImagePosition) ??
        DEFAULT_IMAGE_POSITION,
      // New stats fields from database (allowing 0 values for new accounts)
      account_tier: profileData.account_tier || 'tier0',
      account_level: profileData.account_level || 1,
      account_exp: profileData.account_exp || 0,
      discord_level: profileData.discord_level || 0,
      total_exp_boost: profileData.total_exp_boost || 0,
      likes_count: profileData.likes_count || 0,
      comments_count: profileData.comments_count || 0,
      shares_count: profileData.shares_count || 0,
      research_papers_count: profileData.research_papers_count || 0,
      reputation_score: profileData.reputation_score || 0,
      awards: profileData.awards || [],
    };
    // Transform articles data
    interface DatabaseArticleData {
      id: number;
      title: string | null;
      content: string | null;
      timestamp: string | null;
      created_at: string | null;
      updated_at: string | null;
      likes: number | null;
      shares: number | null;
      comments_count: number | null;
      author: string | null;
      author_avatar: string | null;
      author_id: string | null;
      image_url: string | null;
      tags: string[] | null;
      views: number | null;
    }

    const transformedArticles: Article[] = (articlesResult.data as DatabaseArticleData[] ?? []).map(
      (article: DatabaseArticleData): Article => ({
        id: article.id,
        title: article.title ?? "",
        content: article.content ?? "",
        author: article.author ?? "",
        author_avatar: article.author_avatar ?? undefined,
        author_id: article.author_id ?? "",
        timestamp: article.timestamp || article.created_at || undefined,
        created_at: article.created_at ?? "",
        updated_at: article.updated_at ?? "",
        likes: article.likes ?? 0,
        comments_count: article.comments_count ?? 0,
        image_url: article.image_url ?? undefined,
        tags: article.tags ?? [],
        is_liked: false, // Could be fetched separately if needed
        is_bookmarked: false, // Could be fetched separately if needed
        interaction_count: 0, // Derived field if needed
      })
    );

    const isOwnProfile = currentUser?.id === profileId;

    return (
      <div className="min-h-screen bg-background">
        <ErrorBoundary fallback={<ProfileErrorState />}>
          <Suspense fallback={<ProfileSkeleton />}>
            <ProfileDisplay
              currentUser={currentUser}
              profile={enrichedProfile}
              articles={transformedArticles}
              isOwnProfile={isOwnProfile}
            />
          </Suspense>
        </ErrorBoundary>
      </div>
    );
  } catch (error) {
    console.error("Page error:", error);

    if (error instanceof Error) {
      if (error.message.includes("auth")) {
        redirect(`/ctn/profile/${profileId}?auth=false`);
      } else if (error.message.includes("network")) {
        redirect("/error?message=network-error");
      } else if (error.message.includes("data-fetch")) {
        redirect("/error?message=data-fetch-error");
      } else {
        redirect("/error?message=profile-load-failed");
      }
    } else {
      redirect("/error?message=unknown-error");
    }
  }
}

function ProfileErrorState() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-4">
      <div className="text-center space-y-4">
        <h2 className="text-2xl font-semibold text-destructive">
          Error Loading Profile
        </h2>
        <p className="text-muted-foreground max-w-md">
          There was an error loading this profile. Please try refreshing the
          page or come back later.
        </p>
      </div>
    </div>
  );
}

function ProfileSkeleton() {
  return (
    <div className="space-y-4 p-4 max-w-6xl mx-auto animate-pulse">
      {/* Banner Skeleton */}
      <div className="w-full h-80 bg-muted rounded-lg" />

      {/* Profile Info Skeleton */}
      <div className="relative">
        <div className="absolute -top-16 left-8">
          <div className="w-32 h-32 bg-muted rounded-full border-4 border-background" />
        </div>
        <div className="pt-20 px-8">
          <div className="space-y-4">
            <div className="h-8 w-48 bg-muted rounded" />
            <div className="h-4 w-96 bg-muted rounded" />
            <div className="flex gap-4">
              <div className="h-4 w-24 bg-muted rounded" />
              <div className="h-4 w-24 bg-muted rounded" />
            </div>
          </div>
        </div>
      </div>

      {/* Articles Skeleton */}
      <div className="mt-8">
        <div className="h-6 w-32 bg-muted rounded mb-4" />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-card rounded-lg p-4 space-y-4">
              <div className="h-6 w-3/4 bg-muted rounded" />
              <div className="h-4 w-1/2 bg-muted rounded" />
              <div className="space-y-2">
                <div className="h-4 w-full bg-muted rounded" />
                <div className="h-4 w-full bg-muted rounded" />
                <div className="h-4 w-2/3 bg-muted rounded" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
